"use client";

import { useQuery } from "@tanstack/react-query";
import { ServicesListSchema } from "../../../schemas/services";
import { GetServicesListService } from "../../../services/services/get-services";

export default function ServicePage() {
  const getServicesListService = new GetServicesListService();

  const { data, isLoading, error } = useQuery({
    queryKey: ["services-list"],
    queryFn: async () => {
      const response = await getServicesListService.execute();
      return ServicesListSchema.parse(response.response);
    },
  });

  if (isLoading) return <div>Loading services...</div>;
  if (error) return <div>Error loading services</div>;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Services</h1>
      <div className="grid gap-4">
        {data?.items?.map((service) => (
          <div key={service.id} className="border p-4 rounded">
            <h2 className="font-semibold">{service.name}</h2>
            <p className="text-gray-600">{service.description}</p>
            <p className="text-sm text-gray-500">
              Provider: {service.provider.firstName} {service.provider.lastName}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
